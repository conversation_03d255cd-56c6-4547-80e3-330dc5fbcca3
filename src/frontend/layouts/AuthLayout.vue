<script setup lang="ts">
import Logo from '@/components/ui/Logo.vue'
</script>

<template>
  <div class="min-h-screen bg-base-200">
    <!-- Navigation -->
    <nav class="bg-base-100 shadow-sm border-b border-base-300">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <Logo />
          <div class="flex items-center space-x-4">
            <router-link to="/" class="text-base-content/70 hover:text-base-content">Home</router-link>
            <a href="/admin/login" class="btn btn-primary btn-sm">
              Admin Login
            </a>
          </div>
        </div>
      </div>
    </nav>

    <!-- Main Content -->
    <main class="flex-1">
      <slot />
    </main>

    <!-- Footer -->
    <footer class="bg-base-100 border-t border-base-300 mt-auto">
      <div class="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8">
        <p class="text-center text-sm text-base-content/70">Mail2Webhook.eu Service</p>
      </div>
    </footer>
  </div>
</template>

<!-- Auth layout component - clean layout for login/register pages -->

<style scoped>
/* Auth layout specific styles */
</style>
