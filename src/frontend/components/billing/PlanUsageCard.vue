<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'

interface UsageData {
  usage: {
    emails: number
    domains: number
    webhooks: number
    aliases: number
  }
  limits: {
    emails: number
    domains: number
    webhooks: number
    aliases: number
  }
  percentages: {
    emails: number
    domains: number
    webhooks: number
    aliases: number
  }
  plan: {
    name: string
    type: string
  }
}

// State
const usageData = ref<UsageData | null>(null)
const isLoading = ref(true)
const error = ref<string | null>(null)

// Computed
const emailUsageColor = computed(() => {
  if (!usageData.value) return 'bg-base-300'
  const percentage = usageData.value.percentages.emails
  if (percentage >= 90) return 'bg-error'
  if (percentage >= 75) return 'bg-warning'
  return 'bg-success'
})

const emailUsageTextColor = computed(() => {
  if (!usageData.value) return 'text-base-content/70'
  const percentage = usageData.value.percentages.emails
  if (percentage >= 90) return 'text-error'
  if (percentage >= 75) return 'text-warning'
  return 'text-success'
})

// Methods
const loadUsageData = async () => {
  try {
    isLoading.value = true
    error.value = null
    
    const response = await fetch('/api/billing/usage')
    if (!response.ok) {
      throw new Error('Failed to load usage data')
    }
    
    const data = await response.json()
    if (data.success) {
      usageData.value = data
    } else {
      throw new Error('Invalid response format')
    }
  } catch (err: any) {
    error.value = err.message
    console.error('Failed to load usage data:', err)
  } finally {
    isLoading.value = false
  }
}

const formatUsage = (current: number, limit: number) => {
  if (limit === 0 || !limit) return `${current}`
  return `${current}/${limit}`
}

const getProgressWidth = (percentage: number) => {
  return Math.min(Math.max(percentage, 0), 100)
}

// Lifecycle
onMounted(() => {
  loadUsageData()
})

// Expose refresh method
defineExpose({
  refresh: loadUsageData
})
</script>

<template>
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-lg font-semibold text-gray-900">Usage & Limits</h3>
      <span v-if="usageData" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
        {{ usageData.plan.name }} Plan
      </span>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="flex justify-center py-8">
      <span class="loading loading-spinner loading-md"></span>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="text-center py-8">
      <div class="text-red-600 mb-2">{{ error }}</div>
      <button @click="loadUsageData" class="btn btn-sm btn-outline">
        Try Again
      </button>
    </div>

    <!-- Usage Data -->
    <div v-else-if="usageData" class="space-y-4">
      <!-- Email Usage -->
      <div>
        <div class="flex justify-between items-center mb-2">
          <span class="text-sm font-medium text-gray-700">Emails this month</span>
          <span class="text-sm font-medium" :class="emailUsageTextColor">
            {{ formatUsage(usageData.usage.emails, usageData.limits.emails) }}
          </span>
        </div>
        <div class="w-full bg-gray-200 rounded-full h-2">
          <div 
            class="h-2 rounded-full transition-all duration-300"
            :class="emailUsageColor"
            :style="{ width: `${getProgressWidth(usageData.percentages.emails)}%` }"
          ></div>
        </div>
        <div class="text-xs text-gray-500 mt-1">
          {{ usageData.percentages.emails }}% of monthly limit used
        </div>
      </div>

      <!-- Other Resources -->
      <div class="grid grid-cols-1 sm:grid-cols-3 gap-4 pt-4 border-t border-gray-100">
        <!-- Domains -->
        <div class="text-center">
          <div class="text-2xl font-bold text-gray-900">{{ usageData.usage.domains }}</div>
          <div class="text-sm text-gray-500">
            {{ usageData.limits.domains ? `of ${usageData.limits.domains}` : '' }} domains
          </div>
          <div v-if="usageData.limits.domains" class="w-full bg-gray-200 rounded-full h-1 mt-2">
            <div 
              class="bg-blue-500 h-1 rounded-full"
              :style="{ width: `${getProgressWidth(usageData.percentages.domains)}%` }"
            ></div>
          </div>
        </div>

        <!-- Webhooks -->
        <div class="text-center">
          <div class="text-2xl font-bold text-gray-900">{{ usageData.usage.webhooks }}</div>
          <div class="text-sm text-gray-500">
            {{ usageData.limits.webhooks ? `of ${usageData.limits.webhooks}` : '' }} webhooks
          </div>
          <div v-if="usageData.limits.webhooks" class="w-full bg-gray-200 rounded-full h-1 mt-2">
            <div 
              class="bg-purple-500 h-1 rounded-full"
              :style="{ width: `${getProgressWidth(usageData.percentages.webhooks)}%` }"
            ></div>
          </div>
        </div>

        <!-- Aliases -->
        <div class="text-center">
          <div class="text-2xl font-bold text-gray-900">{{ usageData.usage.aliases }}</div>
          <div class="text-sm text-gray-500">
            {{ usageData.limits.aliases ? `of ${usageData.limits.aliases}` : '' }} aliases
          </div>
          <div v-if="usageData.limits.aliases" class="w-full bg-gray-200 rounded-full h-1 mt-2">
            <div 
              class="bg-green-500 h-1 rounded-full"
              :style="{ width: `${getProgressWidth(usageData.percentages.aliases)}%` }"
            ></div>
          </div>
        </div>
      </div>

      <!-- Upgrade Notice -->
      <div v-if="usageData.percentages.emails >= 80 && usageData.plan.type === 'free'" 
           class="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
        <div class="flex">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-yellow-800">
              Approaching email limit
            </h3>
            <div class="mt-1 text-sm text-yellow-700">
              You've used {{ usageData.percentages.emails }}% of your monthly email quota. 
              Consider upgrading to Pro for 1,000 emails per month.
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
