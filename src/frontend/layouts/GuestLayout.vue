<template>
  <div class="min-h-screen bg-base-100">
    <!-- Navigation -->
    <nav class="bg-base-100 shadow-sm border-b border-base-300">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <div class="flex items-center space-x-4">
              <div class="flex-shrink-0">
                <!-- Logo placeholder -->
                <div class="flex items-center justify-center w-10 h-8 text-xs font-medium text-base-content bg-base-200 border border-base-300 rounded">
                  Logo
                </div>
              </div>
              <h1 class="text-xl font-semibold text-base-content">EU Email Webhook</h1>
            </div>
          </div>
          <div class="flex items-center space-x-4">
            <router-link to="/" class="text-base-content/70 hover:text-base-content">Home</router-link>
            <router-link to="/login" class="text-base-content/70 hover:text-base-content">Sign In</router-link>
            <router-link to="/register" class="bg-primary hover:bg-primary/90 text-primary-content px-4 py-2 rounded-md text-sm font-medium">
              Get Started
            </router-link>
            <a href="/docs" class="text-base-content/70 hover:text-base-content text-sm">
              API Docs
            </a>
          </div>
        </div>
      </div>
    </nav>

    <!-- Main Content -->
    <main class="flex-1">
      <slot />
    </main>

    <!-- Footer -->
    <footer class="bg-base-200 border-t border-base-300 mt-auto">
      <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div class="col-span-1 md:col-span-2">
            <div class="flex items-center space-x-2">
              <div class="flex items-center justify-center w-8 h-6 text-xs font-medium text-base-content bg-base-300 border border-base-300 rounded">
                Logo
              </div>
              <span class="text-lg font-semibold text-base-content">EU Email Webhook</span>
            </div>
            <p class="mt-4 text-sm text-base-content/70">
              GDPR-compliant email-to-webhook service for European businesses.
            </p>
          </div>
          <div>
            <h3 class="text-sm font-semibold text-base-content tracking-wider uppercase">Product</h3>
            <ul class="mt-4 space-y-4">
              <li>
                <a href="/#features" class="text-sm text-base-content/70 hover:text-base-content">Features</a>
              </li>
              <li>
                <a href="/#pricing" class="text-sm text-base-content/70 hover:text-base-content">Pricing</a>
              </li>
              <li>
                <a href="/docs" class="text-sm text-base-content/70 hover:text-base-content">Documentation</a>
              </li>
            </ul>
          </div>
          <div>
            <h3 class="text-sm font-semibold text-base-content tracking-wider uppercase">Support</h3>
            <ul class="mt-4 space-y-4">
              <li>
                <a href="/docs" class="text-sm text-base-content/70 hover:text-base-content">Help Center</a>
              </li>
              <li>
                <a href="mailto:<EMAIL>" class="text-sm text-base-content/70 hover:text-base-content">Contact</a>
              </li>
              <li>
                <a href="/privacy" class="text-sm text-base-content/70 hover:text-base-content">Privacy</a>
              </li>
            </ul>
          </div>
        </div>
        <div class="mt-8 border-t border-base-300 pt-8">
          <p class="text-sm text-base-content/70 text-center">
            © 2025 EU Email Webhook. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
// Guest layout component - for public marketing/info pages
</script>

<style scoped>
/* Guest layout specific styles */
</style>
