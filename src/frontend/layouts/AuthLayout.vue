<template>
  <div class="min-h-screen bg-base-200">
    <!-- Navigation -->
    <nav class="bg-base-100 shadow-sm border-b border-base-300">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <h1 class="text-xl font-semibold text-base-content">EU Email Webhook</h1>
          </div>
          <div class="flex items-center space-x-4">
            <router-link to="/" class="text-base-content/70 hover:text-base-content">Home</router-link>
            <a href="/admin/login" class="btn btn-primary btn-sm">
              Admin Login
            </a>
          </div>
        </div>
      </div>
    </nav>

    <!-- Main Content -->
    <main class="flex-1">
      <slot />
    </main>

    <!-- Footer -->
    <footer class="bg-white border-t border-gray-200 mt-auto">
      <div class="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8">
        <p class="text-center text-sm text-gray-500">EU Email Webhook Service</p>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
// Auth layout component - clean layout for login/register pages
</script>

<style scoped>
/* Auth layout specific styles */
</style>
