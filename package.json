{"name": "eu-email-webhook", "version": "1.0.0", "description": "EU-compliant email to webhook service", "type": "module", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "tsx watch src/index.ts", "dev:frontend": "vite", "build": "npm run build:backend && npm run build:frontend", "build:backend": "tsc", "build:frontend": "vue-tsc --project tsconfig.vue.json --noEmit && vite build", "generate:types": "openapi-typescript http://localhost:3000/docs/json --output src/frontend/types/api-generated.ts", "preview": "vite preview", "test": "jest tests", "test:verify-deployment": "node scripts/verify-deployment.js", "test:unit": "jest tests/unit tests/frontend", "test:integration": "jest tests/integration", "test:watch": "jest tests --watch", "test:coverage": "jest tests --coverage", "test:e2e": "node tests/emails/remote-e2e-test.js", "test:e2e:auth": "node tests/e2e/auth-deployment.test.js", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "clean": "rm -rf dist/ node_modules/.vite/", "docker:dev": "docker-compose --env-file .env up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "docker:clean": "docker-compose down -v --remove-orphans && docker system prune -f"}, "keywords": ["email", "webhook", "gdpr", "eu-compliant", "docker", "nodejs", "typescript"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "dependencies": {"@fastify/cookie": "^11.0.2", "@fastify/cors": "^11.0.1", "@fastify/formbody": "^8.0.2", "@fastify/static": "^8.2.0", "@fastify/swagger": "^9.5.1", "@fastify/swagger-ui": "^5.2.2", "@prisma/client": "^6.9.0", "@tailwindcss/vite": "^4.1.8", "@types/bcrypt": "^5.0.2", "autoprefixer": "^10.4.21", "axios": "^1.7.2", "bcrypt": "^6.0.0", "bull": "^4.12.2", "cssnano": "^7.0.7", "daisyui": "^5.0.43", "dotenv": "^16.5.0", "fastify": "^5.3.3", "ioredis": "^5.4.1", "jsonwebtoken": "^9.0.2", "mailparser": "^3.7.1", "nodemailer": "^6.10.1", "pinia": "^2.2.0", "pino": "^9.1.0", "pino-pretty": "^11.3.0", "postcss": "^8.5.4", "postcss-cli": "^11.0.1", "tailwindcss": "^4.1.8", "uuid": "^10.0.0", "vue": "^3.5.0", "vue-router": "^4.5.1", "zod": "^3.23.8"}, "devDependencies": {"@babel/core": "^7.27.4", "@babel/preset-env": "^7.27.2", "@jest/globals": "^29.7.0", "@tailwindcss/cli": "^4.1.8", "@tailwindcss/postcss": "^4.1.8", "@types/jest": "^29.5.14", "@types/mailparser": "^3.4.6", "@types/node": "^20.14.2", "@types/nodemailer": "^6.4.17", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "@vitejs/plugin-legacy": "^6.1.1", "@vitejs/plugin-vue": "^5.2.0", "@vue/tsconfig": "^0.7.0", "babel-jest": "^29.0.0", "concurrently": "^9.1.2", "eslint": "^8.57.1", "eslint-config-prettier": "^10.1.5", "jest": "^29.7.0", "openapi-typescript": "^7.8.0", "prettier": "^3.5.3", "prisma": "^6.9.0", "supertest": "^7.1.1", "ts-jest": "^29.3.4", "tsx": "^4.19.4", "typescript": "^5.4.5", "vite": "^6.3.5", "vue-tsc": "^2.2.0"}}