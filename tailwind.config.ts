/** @type {import('tailwindcss').Config} */
export default {
  content: [
    './src/frontend/**/*.{vue,js,ts}',
    './public/js/**/*.js',
    './src/**/*.{js,ts}',
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: ['Inter', 'ui-sans-serif', 'system-ui'],
      },
    },
  },
  plugins: [require('daisyui')],
  daisyui: {
    themes: [
      {
        'eu-theme': {
          'primary': '#003366',        // EU deep blue
          'primary-content': '#ffffff',
          'secondary': '#FFCC00',      // EU yellow
          'secondary-content': '#000000',
          'accent': '#335580',
          'accent-content': '#ffffff',
          'neutral': '#263238',
          'neutral-content': '#ffffff',
          'base-100': '#ffffff',
          'base-200': '#F8F9FA',
          'base-300': '#ECEFF1',
          'base-content': '#263238',
          'info': '#0ea5e9',
          'info-content': '#ffffff',
          'success': '#22c55e',
          'success-content': '#ffffff',
          'warning': '#f59e0b',
          'warning-content': '#ffffff',
          'error': '#ef4444',
          'error-content': '#ffffff',
        },
      },
    ],
    base: true,
    styled: true,
    utils: true,
  },
}
