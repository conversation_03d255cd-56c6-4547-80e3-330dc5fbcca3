<script setup lang="ts">
interface Props {
  size?: 'sm' | 'md' | 'lg'
  showText?: boolean
  textClass?: string
}

const props = withDefaults(defineProps<Props>(), {
  size: 'md',
  showText: true,
  textClass: 'text-base-content'
})

const sizeClasses = {
  sm: 'h-6',
  md: 'h-8', 
  lg: 'h-12'
}

const textSizeClasses = {
  sm: 'text-lg',
  md: 'text-xl',
  lg: 'text-2xl'
}
</script>

<template>
  <div class="flex items-center space-x-3">
    <img 
      src="/public/logo.png" 
      alt="Mail2Webhook.eu" 
      :class="sizeClasses[size] + ' w-auto'"
    />
    <span 
      v-if="showText"
      :class="[textSizeClasses[size], 'font-semibold', textClass]"
    >
      Mail2Webhook.eu
    </span>
  </div>
</template>
