<template>
  <div class="min-h-screen bg-base-100">
    <!-- Header -->
    <div class="border-b border-base-300 navbar bg-base-100">
      <div class="flex-1">
        <!-- Logo and Title -->
        <div class="flex items-center space-x-4">
          <div class="flex-shrink-0">
            <img src="/logo.png" alt="Mail2Webhook.eu" class="h-8 w-auto" />
          </div>
          <h1 class="text-xl font-medium text-base-content">
            <router-link to="/domains" class="transition-colors hover:text-primary">Dashboard</router-link>
          </h1>
        </div>
      </div>

      <div class="flex-none">
        <!-- User Dropdown -->
        <div class="dropdown dropdown-end">
          <div tabindex="0" role="button" class="btn btn-ghost btn-circle avatar">
            <div class="w-10 rounded-full">
              <div class="flex items-center justify-center w-10 h-10 bg-primary text-primary-content rounded-full">
                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" />
                </svg>
              </div>
            </div>
          </div>
          <ul tabindex="0" class="menu menu-sm dropdown-content bg-base-100 rounded-box z-[1] mt-3 w-52 p-2 shadow">
            <li class="menu-title">
              <span>{{ user?.name || user?.email || 'User' }}</span>
            </li>
            <li>
              <router-link to="/settings" class="flex items-center gap-2">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                <span>Settings</span>
              </router-link>
            </li>
            <li>
              <button @click="logout" class="flex items-center w-full gap-2 text-left">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                </svg>
                <span>Logout</span>
              </button>
            </li>
          </ul>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <main class="px-6 mx-auto max-w-7xl lg:px-8">
      <slot />
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'

// Props
interface User {
  id: string
  email: string
  name?: string
}

const props = defineProps<{
  user?: User
}>()

// Router
const router = useRouter()

// User data
const user = ref<User | null>(props.user || null)

// Load user data if not provided
const loadUserData = async () => {
  if (!user.value) {
    try {
      const response = await fetch('/api/dashboard/metrics')
      if (response.ok) {
        // We'll get user data from the metrics endpoint or create a separate user endpoint
        // For now, we'll extract from cookie or make a separate call
        user.value = { id: '', email: 'User', name: 'User' }
      }
    } catch (error) {
      console.error('Failed to load user data:', error)
    }
  }
}

// Logout function
const logout = async () => {
  try {
    const response = await fetch('/user/logout', {
      method: 'POST',
      credentials: 'include'
    })
    
    if (response.ok) {
      // Redirect to login
      window.location.href = '/login'
    } else {
      console.error('Logout failed')
    }
  } catch (error) {
    console.error('Logout error:', error)
  }
}

onMounted(() => {
  loadUserData()
})
</script>

<style scoped>
/* Dashboard layout specific styles */
</style>
